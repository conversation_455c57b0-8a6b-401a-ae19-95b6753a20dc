import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { textSizes } from '@styles/vars';
import GooglePlacesAutocomplete, { geocodeByPlaceId } from 'react-google-places-autocomplete';
import { Grid, Typography } from '@mui/material';
import Field, { Input } from '@ui/Field';

const AddressContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
`;

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
`;

const AddressRow = styled.div`
  display: flex;
  gap: 12px;
  width: 100%;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
  }
`;

const AddressField = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const SecondaryLabel = styled.label`
  font-size: ${textSizes["12"]};
  color: #666;
  margin-bottom: 4px;
  display: block;
`;

const StyledInput = styled(Input)`
  width: 100%;
`;

const AutocompleteWrapper = styled.div`
  position: relative;
  width: 100%;
  
  .google-places-autocomplete {
    width: 100%;
  }
  
  .google-places-autocomplete__input {
    width: 100% !important;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: ${textSizes["14"]};
    transition: border-color 0.2s ease;
    
    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    
    &.error {
      border-color: #dc3545;
    }
  }
  
  .google-places-autocomplete__suggestions-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
  }
  
  .google-places-autocomplete__suggestion {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .google-places-autocomplete__suggestion--active {
    background-color: #e3f2fd;
  }
`;

const ErrorText = styled(Typography)`
  color: #dc3545;
  font-size: ${textSizes["12"]};
  margin-top: 4px;
`;

const AddressInput = ({
  value = {},
  onChange,
  error = {},
  disabled = false,
  required = false,
  apiKey,
  placeholder = "Start typing your address...",
  ...props
}) => {
  const [placeVal, setPlaceVal] = useState(null);
  const [addressComponents, setAddressComponents] = useState({
    streetNumber: value.streetNumber || '',
    streetName: value.streetName || '',
    addressLine2: value.addressLine2 || '',
    city: value.city || '',
    state: value.state || '',
    postalCode: value.postalCode || '',
    country: value.country || '',
    formattedAddress: value.formattedAddress || ''
  });

  const debounceRef = useRef(null);

  // Parse Google Places result into address components
  const parseAddressComponents = (addressComponents, formattedAddress) => {
    const components = {
      streetNumber: '',
      streetName: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      countryCode: '',
      formattedAddress: formattedAddress
    };

    addressComponents.forEach(component => {
      const types = component.types;
      
      if (types.includes('street_number')) {
        components.streetNumber = component.long_name;
      } else if (types.includes('route')) {
        components.streetName = component.long_name;
      } else if (types.includes('locality')) {
        components.city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        components.state = component.long_name;
      } else if (types.includes('postal_code')) {
        components.postalCode = component.long_name;
      } else if (types.includes('country')) {
        components.country = component.long_name;
        components.countryCode = component.short_name;
      }
    });

    return components;
  };

  // Handle Google Places selection
  useEffect(() => {
    async function fetchPlaceInfo() {
      if (placeVal) {
        try {
          const [res] = await geocodeByPlaceId(placeVal?.value?.place_id);
          if (res) {
            const parsedComponents = parseAddressComponents(
              res.address_components, 
              res.formatted_address
            );
            
            const fullAddress = {
              ...parsedComponents,
              addressLine2: addressComponents.addressLine2, // Preserve manually entered line 2
              lat: res.geometry.location.lat(),
              lng: res.geometry.location.lng(),
              placeId: res.place_id,
            };

            setAddressComponents(fullAddress);
            onChange && onChange(fullAddress);
          }
        } catch (error) {
          console.error('Error fetching place details:', error);
        }
      }
    }

    fetchPlaceInfo();
  }, [placeVal]);

  // Handle manual input changes with debouncing
  const handleInputChange = (field, inputValue) => {
    const updatedComponents = {
      ...addressComponents,
      [field]: inputValue
    };

    // Update formatted address when components change
    if (field !== 'formattedAddress') {
      const parts = [
        updatedComponents.streetNumber,
        updatedComponents.streetName,
        updatedComponents.addressLine2,
        updatedComponents.city,
        updatedComponents.state,
        updatedComponents.postalCode,
        updatedComponents.country
      ].filter(Boolean);
      
      updatedComponents.formattedAddress = parts.join(', ');
    }

    setAddressComponents(updatedComponents);

    // Debounce the onChange callback
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    debounceRef.current = setTimeout(() => {
      onChange && onChange(updatedComponents);
    }, 300);
  };

  // Initialize from external value changes
  useEffect(() => {
    if (value && Object.keys(value).length > 0) {
      setAddressComponents(prev => ({
        ...prev,
        ...value
      }));
    }
  }, [value]);

  return (
    <AddressContainer {...props}>
      {/* Primary Address Line with Autocomplete */}
      <AddressField>
        <Label htmlFor="address-autocomplete">
          Address {required && '*'}
        </Label>
        <AutocompleteWrapper>
          <GooglePlacesAutocomplete
            apiKey={apiKey}
            selectProps={{
              value: placeVal,
              onChange: setPlaceVal,
              placeholder: placeholder,
              isDisabled: disabled,
              id: "address-autocomplete",
              isClearable: true,
              styles: {
                input: (provided) => ({
                  ...provided,
                  margin: 0,
                }),
                control: () => ({}),
                singleValue: () => ({}),
                placeholder: () => ({}),
              }
            }}
            autocompletionRequest={{
              types: ['address'],
              componentRestrictions: {
                // You can restrict to specific countries if needed
                // country: ['us', 'ca']
              }
            }}
          />
        </AutocompleteWrapper>
        {error.formattedAddress && (
          <ErrorText variant="caption">
            {error.formattedAddress}
          </ErrorText>
        )}
      </AddressField>

      {/* Street Number and Street Name Row */}
      <AddressRow>
        <AddressField style={{ flex: '0 0 120px' }}>
          <SecondaryLabel htmlFor="street-number">Street Number</SecondaryLabel>
          <StyledInput
            id="street-number"
            type="text"
            value={addressComponents.streetNumber}
            onChange={(e) => handleInputChange('streetNumber', e.target.value)}
            placeholder="123"
            disabled={disabled}
          />
        </AddressField>
        <AddressField>
          <SecondaryLabel htmlFor="street-name">Street Name</SecondaryLabel>
          <StyledInput
            id="street-name"
            type="text"
            value={addressComponents.streetName}
            onChange={(e) => handleInputChange('streetName', e.target.value)}
            placeholder="Main Street"
            disabled={disabled}
          />
        </AddressField>
      </AddressRow>

      {/* Secondary Address Line */}
      <AddressField>
        <SecondaryLabel htmlFor="address-line-2">
          Apartment, suite, unit, building, floor, etc. (Optional)
        </SecondaryLabel>
        <StyledInput
          id="address-line-2"
          type="text"
          value={addressComponents.addressLine2}
          onChange={(e) => handleInputChange('addressLine2', e.target.value)}
          placeholder="Apt 4B, Suite 100, Floor 2, etc."
          disabled={disabled}
        />
      </AddressField>

      {/* City, State, Postal Code Row */}
      <AddressRow>
        <AddressField>
          <SecondaryLabel htmlFor="city">City</SecondaryLabel>
          <StyledInput
            id="city"
            type="text"
            value={addressComponents.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            placeholder="City"
            disabled={disabled}
          />
        </AddressField>
        <AddressField>
          <SecondaryLabel htmlFor="state">State/Province</SecondaryLabel>
          <StyledInput
            id="state"
            type="text"
            value={addressComponents.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
            placeholder="State"
            disabled={disabled}
          />
        </AddressField>
        <AddressField style={{ flex: '0 0 140px' }}>
          <SecondaryLabel htmlFor="postal-code">Postal Code</SecondaryLabel>
          <StyledInput
            id="postal-code"
            type="text"
            value={addressComponents.postalCode}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            placeholder="12345"
            disabled={disabled}
          />
        </AddressField>
      </AddressRow>

      {/* Country */}
      <AddressField>
        <SecondaryLabel htmlFor="country">Country</SecondaryLabel>
        <StyledInput
          id="country"
          type="text"
          value={addressComponents.country}
          onChange={(e) => handleInputChange('country', e.target.value)}
          placeholder="Country"
          disabled={disabled}
        />
      </AddressField>
    </AddressContainer>
  );
};

export default AddressInput;
